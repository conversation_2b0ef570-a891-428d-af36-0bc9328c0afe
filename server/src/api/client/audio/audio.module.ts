import { UserAudioListened } from "@/entity/UserAudioListened"
import { HttpModule } from "@nestjs/axios"
import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from "@nestjs/typeorm"
import { AudioController } from './audio.controller'
import { AudioService } from './audio.service'

@Module({
  imports: [HttpModule, ConfigModule, TypeOrmModule.forFeature([UserAudioListened])],
  controllers: [AudioController],
  providers: [AudioService],
})
export class AudioModule {}
