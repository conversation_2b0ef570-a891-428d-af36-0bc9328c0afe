import { Audio } from "@/entity/Audio"
import { AudioFavourite } from "@/entity/AudioFavourite"
import { AudioLike } from "@/entity/AudioLike"
import { AudioTag } from "@/entity/AudioTag"
import { Library } from "@/entity/Library"
import { LibraryTranslation } from "@/entity/LibraryTranslation"
import { Notification, NotificationType } from '@/entity/Notification'
import { UserAudioListened } from "@/entity/UserAudioListened"
import { EpubParser } from '@/utils/epub.parser'
import { HttpService } from "@nestjs/axios"
import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { copyFileSync, createReadStream, existsSync, rmSync } from "fs"
import * as moment from 'moment'
import { parseStream } from 'music-metadata'
import { basename, dirname, extname, join } from "path"
import { firstValueFrom } from 'rxjs'
import * as sharp from 'sharp'
import slugify from "slugify"

@Injectable()
export class FirebaseService {
    constructor(
        private readonly configService: ConfigService,
        private readonly httpService: HttpService
    ) {}

    async getItem(key: string) {
        return await LibraryTranslation.findOneBy({external_id: key})
    }

    async create(body: any) {
        const baseUrl = process.env.BUILD_ENV === 'local' ? 'http://localhost:9015' : 'https://dev.advayta.org'

        const code = slugify(body.title, {lower: true})
        let fields: any = { code, external_id: body.key }

        for(let key in LibraryTranslation) {
            if(!body[key]) continue
            fields[key] = body[key]
        }

        if(body.link) {
            const fileLink = this.copyFileFromTemp('link', body, 'books/epub');
            const epubParser = new EpubParser(fileLink[1])
            //const epub = new EpubReader(fileLink[1]);
            body.link = fields.link = fileLink[0];
            try {
                fields.chapters = await epubParser.parse()
            } catch(e) {
                rmSync(fileLink[1], {recursive: true});
                throw new InternalServerErrorException('Неверный формат книги. Возможно, отсутствует содержание.')
            }
        }

        if(body.image) {
            body.image = fields.image = this.copyFileFromTemp('image', body, 'books/img')[0];
            body.image_mobile = baseUrl + '/upload/' + await this.changeImageResolution(`./upload/firebase/books/img/${basename(body.image)}`);
        }

        body.audiolist = body.audiolist && Array.isArray(body.audiolist) ? body.audiolist.filter(e => e.chapter || e.url) : []

        for(let i in body.audiolist) {
            if(body.audiolist[i]?.url) {
                const uploadIndex = body.audiolist[i].url.indexOf('/upload');
                const result = '.' + body.audiolist[i].url.substring(uploadIndex);
                const bookName = basename(body.audiolist[i].url)
                const newPath = `./upload/firebase/books/mp3/${bookName}`;
                if(body.audiolist[i].url.indexOf('upload/tmp') == -1) continue;
                if(existsSync(result)) {
                    copyFileSync(result, newPath);
                }
                body.audiolist[i].url = `${baseUrl}/upload/firebase/books/mp3/${bookName}`;
            }
        }


        fields = {
            code,
            lang: body.lang,
            external_id: body.key,
            title: body.title,
            seo_title: body.seo_title,
            seo_description: body.seo_description,
            pages: body.pages,
            recommendation: body.recommendation,
            image: body.image,
            image_mobile: body.image_mobile,
            access: body.access,
            annotation: body.annotation,
            content: body.content,
            format: body.format,
            link: body.link,
            author: body.bookAuthor,
            reader: body.bookReader,
            category: body.bookCategory,
            audio: body.audiolist && Array.isArray(body.audiolist) ? body.audiolist.filter(e => e.chapter || e.url) : [],
            tagsString: Array.isArray(body.tags) ? body.tags.map(e => e.value).join(', ') : body.tags,
            linkShop: body.link_shop,
            linkShopOnline: body.link_shop_online,
            duration: body.duration,
            chapters: fields.chapters,
            paid: body.paid || false,
            priceEur: body.priceEur || null,
            priceRub: body.priceRub || null,
        }



        for(let i in fields) {
            if(fields[i] && typeof fields[i] === 'object' && 'key' in fields[i]) {
                fields[i] = fields[i]['value'];
            }
        }

        let tagEntities: AudioTag[] = [];

        if (Array.isArray(body.tags)) {
            for (let tagObj of body.tags) {
                const tagValue = tagObj.value?.trim();
                const tagKey = tagObj.key;
                if (!tagValue || !tagKey) continue;

                let tag = await AudioTag.findOneBy({ external_id: tagKey });

                if (tag) {
                    // Если тег найден по external_id, но name отличается — обновляем
                    if (tag.name != tagValue) {
                        tag.name = tagValue;
                        await tag.save();
                    }
                } else {
                    // Если по external_id не нашли — пробуем найти по name
                    tag = await AudioTag.findOneBy({ name: tagValue });

                    if (!tag) {
                        // Если вообще не нашли — создаём
                        tag = AudioTag.create({ name: tagValue, external_id: tagKey });
                        await tag.save();
                    }
                }

                tagEntities.push(tag);
            }
        }

        let libraryTranslation = await LibraryTranslation.findOneBy({external_id: body.key})
        if(libraryTranslation) {
            await LibraryTranslation.update(libraryTranslation.id, fields)
            libraryTranslation = await LibraryTranslation.findOne({
                where: { id: libraryTranslation.id },
                relations: ['tags']
            });
            libraryTranslation.tags = tagEntities;
            await libraryTranslation.save();
            return libraryTranslation;
        }

        let translation = LibraryTranslation.create(fields)
        translation.tags = tagEntities;
        translation = await translation.save();
        let library = await this.createLibrary(body)
        library.translations = [...!library.translations ? [] : library.translations, translation]
        await library.save();

        await Notification.save({
            type: fields.paid || fields.priceEur || fields.priceRub ? NotificationType.CONTENT_PREMIUM_ADDED : NotificationType.CONTENT_BOOK_PUBLISHED,
            title: body.title,
            link: `/ru/library/${fields.code}`
        })

        return fields
    }

    async update(key: string, body: any) {
        const item = await this.getItem(key)
        if(!item) return await this.create(body)
    }

    async createLibrary(body: any) {
        const code = slugify(body.title, {lower: true})
        if(!body.parent) {
            let library = await this.getLibrary(code)
            if(library) return library
            await Library.save({code})
            return await this.getLibrary(code)
        }

        let translation = await LibraryTranslation.findOneBy({external_id: body.parent})
        return await this.getLibrary(translation.code)
    }

    async getLibrary(code: string) {
       return await Library.findOne({
           where: { code },
           relations: {
               translations: true
           }
       })
    }

    async removeBook(key: string) {
        const library = await LibraryTranslation.findOne({
            where: {
                external_id: key
            },
            relations: {
                library: true
            }
        })

        if(library.audio && library.audio.length) {
            for(let item of (library as any).audio) {
                if(!item?.url) continue;
                this.removeFile(item.url)
            }
        }

        if(library.link) this.removeFile(library.link)
        if(library.image) this.removeFile(library.image)

        if(!library) return false
        return await Library.delete(library.library.id)
    }

    async removeLecture(key: string) {
        const audio = await Audio.findOne({
            where: {external_id: key},
            relations: ['likes', 'favourites', 'playlists']
        })
        if(audio) {
            for(let like of audio.likes) {
                await AudioLike.delete(like.id)
            }
            for(let fav of audio.favourites) {
                await AudioFavourite.delete(fav.id)
            }
            let listenedAudios = await UserAudioListened.find({
                where: {
                    audio: {
                        id: audio.id
                    }
                }
            })
            for(let a of listenedAudios) {
                await UserAudioListened.delete(a.id)
            }

            if(audio.link) this.removeFile(audio.link)
            if(audio.text) this.removeFile(audio.text)

            return await Audio.delete(audio.id)
        }
        return false
    }

    async createTag(body: any) {
        return await AudioTag.save({
            external_id: body.key,
            name: body.value
        })
    }

    copyFileFromTemp(key, body, folder) {
        const baseUrl = process.env.BUILD_ENV === 'local' ? 'http://localhost:9015' : 'https://dev.advayta.org'
        const uploadIndex = body[key].indexOf('/upload');
        const result = '.' + body[key].substring(uploadIndex);
        const bookName = basename(body[key])
        const newPath = `./upload/firebase/${folder}/${bookName}`;
        if(body[key].indexOf('upload/tmp') == -1) return [`${baseUrl}/upload/firebase/${folder}/${bookName}`,newPath];
        if(existsSync(result)) {
            copyFileSync(result, newPath);
        }
        return [`${baseUrl}/upload/firebase/${folder}/${bookName}`, newPath];
    }

    async updateTag(body: any) {
        let tag = await AudioTag.findOneBy({ external_id: body.key });
        if(!tag) return;

        if(body.value != tag.name) {
            tag.name = body.value;
            await tag.save();
        }

        return true;
    }

    async getAudioDuration(path: string) {
        const uploadIndex = path.indexOf('/upload');
        const result = '.' + path.substring(uploadIndex);
        try {
            const metadata = await parseStream(createReadStream(result));
            if(metadata?.format?.duration) {
                return Math.ceil(metadata.format.duration)
            }
            return 0
        } catch(e) {
            return 0
        }
    }

    removeFile(link: string) {
        const uploadIndex = link.indexOf('/upload');
        const result = '.' + link.substring(uploadIndex);

        if(existsSync(result)) {
            rmSync(result, {recursive: true});
        }
    }

    async checkExists(title: string) {
        const code = slugify(title, {lower: true})
        return !!(await LibraryTranslation.findOneBy({ code }));
    }

    async changeImageResolution(path: string) {
        const newFileName = basename(path, extname(path)) + '_mobile' + extname(path);
        const newPath = join(dirname(path), newFileName);
        await sharp(path).resize(300, 200, {fit: 'inside'}).toFile(newPath);
        return newPath.replace('upload/', '');
    }

    async getSupabaseLectures(date) {
        try {
					  const token = this.configService.get('SUPABASE_API_TOKEN');
            const response = await firstValueFrom(this.httpService.get(
                `${this.configService.get('SUPABASE_API_URL')}/document_processing_view`, 
                    {
                        headers: {
                            apikey: token,
                            Authorization: `Bearer ${token}`
                        },
                    params: {
                        lecture_date: `eq.${moment(date).format('YYYY-MM-DD')}`
                    } 
                }
            ));
						
						return response.data;
        } catch(e) {
					throw new BadRequestException;
				}
    }
}
