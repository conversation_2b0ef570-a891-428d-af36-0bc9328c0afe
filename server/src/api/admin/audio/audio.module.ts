import { FirebaseModule } from '@/api/firebase/firebase.module'
import { Audio } from "@/entity/Audio"
import { AudioAuthor } from "@/entity/AudioAuthor"
import { AudioFavourite } from "@/entity/AudioFavourite"
import { AudioHandler } from "@/entity/AudioHandler"
import { AudioLike } from "@/entity/AudioLike"
import { AudioPosition } from "@/entity/AudioPosition"
import { AudioStatus } from "@/entity/AudioStatus"
import { AudioTag } from "@/entity/AudioTag"
import { AudioType } from "@/entity/AudioType"
import { Content } from "@/entity/Content"
import { ContentCategory } from "@/entity/ContentCategory"
import { VideoStatus } from "@/entity/VideoStatus"
import { HttpModule } from '@nestjs/axios'
import { Module } from '@nestjs/common'
import { TypeOrmModule } from "@nestjs/typeorm"
import { AudioController } from './audio.controller'
import { AudioService } from './audio.service'

@Module({
  imports: [
    TypeOrmModule.forFeature([Audio, AudioAuthor, AudioStatus, AudioType, VideoStatus, AudioLike, AudioFavourite, AudioPosition, AudioTag, AudioHandler, Content, ContentCategory]),
    FirebaseModule,
    HttpModule
  ],
  controllers: [AudioController],
  providers: [AudioService],
})
export class AudioModule {}
