import { AudioFavourite } from "@/entity/AudioFavourite"
import { AudioLike } from "@/entity/AudioLike"
import { AudioPosition } from "@/entity/AudioPosition"
import { AudioTag } from "@/entity/AudioTag"
import { UserAudioListened } from "@/entity/UserAudioListened"
import {
    BaseEntity,
    Column,
    Entity,
    JoinColumn,
    JoinTable,
    ManyToMany,
    OneToMany,
    PrimaryGeneratedColumn
} from "typeorm"

@Entity()
export class Audio extends BaseEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    external_id: string;

    @Column()
    status: string;

    @Column({nullable: true})
    videoStatus: string;

    @Column({nullable: true})
    youtube: string;

    @Column()
    title: string;

    @Column({nullable: true})
    link: string;

    @Column()
    author: string;

    @Column({nullable: true})
    reader: string;

    @Column({nullable: true})
    comment: string;

    @Column({type: 'date', nullable: true})
    date: Date;

    @Column({nullable: true})
    description: string;

    @Column({nullable: true})
    duration: number;

    @OneToMany(() => AudioLike, like => like.audio, {cascade: true, onDelete: 'CASCADE'})
    likes: AudioLike[];

    @OneToMany(() => AudioFavourite, favourite => favourite.audio, {cascade: true, onDelete: 'CASCADE'})
    favourites: AudioFavourite[];

    @Column({default: 0})
    views: number;

    // @ManyToMany(() => Playlist, playlist => playlist.items, { cascade: true, onDelete: 'CASCADE' })
    // playlists: Playlist;

    @Column({nullable: true})
    seo_title: string;

    @Column({nullable: true})
    seo_description: string;

    @Column({nullable: true})
    text: string;

    @Column({nullable: true})
    text_link: string;

    @Column({nullable: true})
    preview: string;

    @Column({default: false})
    paid: boolean;

    @ManyToMany(() => AudioTag, tag => tag.audios, { cascade: true, onDelete: 'CASCADE' })
    @JoinTable()
    tags: AudioTag[];

    @OneToMany(() => UserAudioListened, user => user.audio)
    @JoinColumn()
    listened: UserAudioListened;

    @OneToMany(() => AudioPosition, position => position.audio, {cascade: true, onDelete: 'CASCADE'})
    position: AudioPosition;

    @Column({nullable: true})
    lecture_link: string
}