.registration-page {
    height: 843px;
    max-width: 490px;
    padding: 0 20px;
    margin: 0 auto;
    .registration-title {
        font-family: BeaumarchaisC;
        font-weight: 400;
        font-size: 50px;
        line-height: 50px;
        letter-spacing: 0;
        text-align: center;
        vertical-align: middle;
        color: var(--font-color1);
        padding-bottom: 16px;
    }
    form {
        width: 100%;
        .form-control {
            display: flex;
            flex-direction: column;
            gap: 6px;
            label {
                font-family: Prata;
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                letter-spacing: 0;
                color: var(--text-color);
            }
            .input-wrap {
                position: relative;
                input {
                    padding-right: 50px;
                }
                .password-suffix {
                    position: absolute;
                    background: url(assets/images/icons/hide.svg);
                    width: 28px;
                    height: 21px;
                    cursor: pointer;
                    top: 50%;
                    transform: translateY(-50%);
                    right: 15px;
                    &:hover {
                        opacity: 0.7;
                    }
                }
            }
            input {
                width: 450px;
                max-width: 100%;
                height: 50px;
                border-radius: 15px;
                outline: none;
                padding: 13px 25px;
                border: 1px solid var(--text-color);
                background: transparent;
                margin: 0 auto;
                font-family: Prata;
                font-weight: 400;
                font-size: 20px;
                line-height: 24px;
                letter-spacing: 0;
                color: var(--font-color1);
            }
        }

        .button_sign_in {
            background: url(assets/images/login-button_1.svg);
            width: 353px;
            height: 50px;
            cursor: pointer;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 20px;
            letter-spacing: 0;
            text-align: center;
            vertical-align: middle;
            padding: 15px 25px;
            color: var(--font-color1);
            margin: 0 auto;
            margin-top: 40px;
        }
        .button_sign_in_google {
            background: url(assets/images/login-button_2.svg);
            width: 353px;
            height: 50px;
            cursor: pointer;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 20px;
            letter-spacing: 0;
            text-align: center;
            vertical-align: middle;
            padding: 15px 25px;
            color: var(--font-color1);
            margin: 5px auto 12px;
            span {
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }
            &[disabled] {
                pointer-events: none;
                cursor: auto;
                opacity: 0.5;
            } 
        }
        .already-have-an-account {
            display: block;
            font-family: Prata;
            font-weight: 400;
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 0;
            text-align: center;
            color: var(--text-color);
            a {
                color: var(--font-color1);
            }
        }

        .form_error {
            background: linear-gradient(135deg, #ffe6e6 0%, #ffcccc 100%);
            border: 1px solid #ff9999;
            border-radius: 10px;
            padding: 12px 20px;
            margin: -10px 0 10px 0;
            font-family: Prata;
            font-weight: 400;
            font-size: 14px;
            line-height: 18px;
            color: #cc0000;
            text-align: center;
            position: relative;
            box-shadow: 0 2px 8px rgba(255, 153, 153, 0.2);

            &::before {
                content: '⚠';
                font-size: 16px;
                margin-right: 8px;
                color: #ff6666;
            }
        }
    }
}

@media (max-width: 768px) {
    .registration-page {
        zoom: 0.9;

        form .form_error {
            font-size: 13px;
            line-height: 16px;
            padding: 10px 16px;
            margin: -8px 0 8px 0;
        }
    }
}
@media (max-width: 500px) {
    .registration-page {
        zoom: 0.8;

        form .form_error {
            font-size: 12px;
            line-height: 15px;
            padding: 8px 14px;
            margin: -6px 0 6px 0;
            border-radius: 8px;
        }
    }
}