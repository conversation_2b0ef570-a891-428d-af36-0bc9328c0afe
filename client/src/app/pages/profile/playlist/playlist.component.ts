import { ShareDataService } from "@/services/share-data.service";
import { Component, ElementRef, inject, PLATFORM_ID, ViewChild } from '@angular/core';
import { NgOptimizedImage } from "@angular/common";
import { PlaylistService } from "@/services/playlist.service";
import { ProfileService } from "@/services/profile.service";
import { RouterModule } from "@angular/router";
import { FormsModule } from "@angular/forms";
import { Track } from "@/interfaces/track";
import { CommonModule } from '@angular/common';
import { ToasterService } from "@/services/toaster.service";
import { AuthService } from "@/services/auth.service";
import { AudioService } from "@/services/audio.service";
import {AudioFilesService} from "@/services/audiofiles.service";
import { LoadingIndicatorComponent } from "@/components/loading-indicator/loading-indicator.component";

@Component({
  selector: 'app-playlist',
  standalone: true,
  imports: [
    RouterModule,
    FormsModule,
    CommonModule,
    NgOptimizedImage,
    LoadingIndicatorComponent
  ],
  templateUrl: './playlist.component.html',
  styleUrl: './playlist.component.scss'
})
export class PlaylistComponent {
  shareDataService = inject(ShareDataService);
  playlistService = inject(PlaylistService);
  profileService = inject(ProfileService);
  platformId = inject(PLATFORM_ID)
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild('renameModal') renameModal!: ElementRef<HTMLDialogElement>;
  authService = inject(AuthService);
  audioService = inject(AudioService);
  toasterService = inject(ToasterService);
  audiofilesService = inject(AudioFilesService)
  message: string = "";
  playlist: any = [];
  selectedPlayList: any = null;
  isLoading: boolean = true;
  selectedDropdElement: any = null;
  draggedIndex: number | null = null;
  currentTrackKey: string | null = null;
  currentTrackIndex: number = 0;
  editingPlaylistId: number | null = null;
  editingPlaylistName: string = '';

  playlistsActions = [
    'редактировать',
    'удалить',
    'воспроизвести',
  ];
  selectedPlaylistsActions = [
    'поделиться',
    'мне нравится',
    'удалить',
    'добавить в очередь',
  ];

  ngOnInit(): void {
    this.getPlaylist()
  }



  getPlaylist() {
    this.isLoading = true;
    this.profileService.getPlaylist().subscribe({
      next: (res: any) => {
        this.playlist = res;
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
      }
    });
  }

  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  removePlaylist(id: number) {
    this.openConfirmationDialog('Удалить плейлист?').then((confirmed) => {
      if (confirmed) {
        this.playlistService.delete(id).subscribe({
          next: () => {
            this.toasterService.showToast('Удален плейлист!', 'success', 'bottom-middle', 3000);
            this.getPlaylist()
          },
          error: () => {
            this.toasterService.showToast('Ошибка удаления, попробуйте еще раз!', 'error', 'bottom-middle', 3000);
            this.getPlaylist()
          }
        });
      }
    });
  }

  formatTime(time: number): string {
    const minutes: number = Math.floor(time / 60);
    const seconds: number = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }

  addToPlaylist(track: Track) {
    this.shareDataService.addToPlaylist(track, false)
    this.toasterService.showToast('Лекция добавлена в очередь', 'success', 'bottom-middle', 3000);
  }

  play(items: Track[]) {
    this.shareDataService.changePlaylist([...items])
  }

  removeTrackFromPlaylist(playlistId: number, trackId: number) {
    this.openConfirmationDialog('Удалить трек?').then((confirmed) => {
      if (confirmed) {
        this.playlistService.removeTrack(playlistId, trackId).subscribe({
          next: () => {
            this.toasterService.showToast('Удален трек!', 'success', 'bottom-middle', 3000);
            const trackIndex = this.selectedPlayList.items.findIndex((e: any) => e.id === trackId);
            this.selectedPlayList.items.splice(trackIndex, 1);
          },
          error: () => {
            this.toasterService.showToast('Ошибка удаления, попробуйте еще раз!', 'error', 'bottom-middle', 3000);
          }
        });
      }
    });
  }

  renamePlaylist(id: number, name: string) {
    this.playlistService.rename(id, name).subscribe({
      next: () => {
        this.toasterService.showToast('Плейлист переименован!', 'success', 'bottom-middle', 3000);
        this.closeRenameModal();
        this.getPlaylist();
      },
      error: () => {
        this.toasterService.showToast('Ошибка переименования, попробуйте еще раз!', 'error', 'bottom-middle', 3000);
      }
    });
  }

  openRenameModal(playlist: any) {
    this.editingPlaylistId = playlist.id;
    this.editingPlaylistName = playlist.name;
    this.renameModal.nativeElement.showModal();
    // Фокус на input после открытия модалки
    setTimeout(() => {
      const input = this.renameModal.nativeElement.querySelector('input');
      if (input) {
        input.focus();
        input.select();
      }
    }, 100);
  }

  closeRenameModal() {
    this.renameModal.nativeElement.close();
    this.editingPlaylistId = null;
    this.editingPlaylistName = '';
  }

  savePlaylistName() {
    if (this.editingPlaylistName.trim() && this.editingPlaylistId) {
      this.renamePlaylist(this.editingPlaylistId, this.editingPlaylistName.trim());
    } else {
      this.closeRenameModal();
    }
  }

  selectPlaylist(playlist: any) {
    this.selectedPlayList = playlist;
  }

  like(track: any) {
    if (!this.authService.token()) {
      this.shareDataService.showInfoModal('error');
      return
    }

    const type = track?.url ? 'audiofilesService' : 'audioService';
    this[type].like(track.id).subscribe(() => {
      const trackIndex = this.selectedPlayList.items.findIndex((track: any) => track.id === track.id);
      if (trackIndex !== -1) {
        this.selectedPlayList.items[trackIndex].liked = !this.selectedPlayList.items[trackIndex].liked;
      }
    })
  }

  isLiked(item: any) {
    return item.liked;
  }

  share(item: any) {
    navigator.clipboard.writeText('https://dev.advayta.org/' + 'ru/audiogallery/audiolektsii/' + item.external_id).then(() => {
      this.toasterService.showToast('Ссылка на аудио скопирована в буфер обмена!', 'success', 'bottom-middle', 3000)
    }).catch((error) => {
      console.error('Unable to copy text to clipboard', error);
    });
  }

  closeMobileActionSelect() {
    this.selectedDropdElement = null;
  }

  showMobileActionOptions(quote: any) {
    this.selectedDropdElement = quote;
  }

  onClickMobileAction(item: any, action: string, actionType: 'track' | 'playlist') {
    if (action == 'удалить' && actionType == 'playlist') {
      this.removePlaylist(item.id);
    }
    if (action == 'удалить' && actionType == 'track') {
      this.removeTrackFromPlaylist(this.selectedPlayList.id, item.id)
    }
    if (action == 'редактировать' && actionType == 'playlist') {
      this.openRenameModal(item);
    }
    this.closeMobileActionSelect();
  }

  onDragStart(index: number, event: DragEvent) {
    event.dataTransfer?.setData('text/plain', index.toString());
    this.draggedIndex = index;
    this.currentTrackKey = this.selectedPlayList.items[this.currentTrackIndex]?.external_id || null;
    event.stopPropagation();
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onDrop(event: DragEvent, dropIndex: number) {
    event.preventDefault();
    if (this.draggedIndex !== null && this.draggedIndex !== dropIndex) {
      const draggedItem = this.selectedPlayList.items[this.draggedIndex];
      this.selectedPlayList.items.splice(this.draggedIndex, 1);
      this.selectedPlayList.items.splice(dropIndex, 0, draggedItem);
      this.saveAudioOrder()
    }
    this.draggedIndex = null;
    if (this.currentTrackKey !== this.selectedPlayList.items[this.currentTrackIndex]?.external_id) {
      this.currentTrackIndex = this.selectedPlayList.items.findIndex((trk: any) => trk.external_id === this.currentTrackKey);
    }
  }

  saveAudioOrder() {
    const audioOrder = this.selectedPlayList.items.map((item: any, index: number) => ({
      playlistId: this.selectedPlayList.id,
      trackId: item.id,
      order: index
    }));

    this.playlistService.updateAudioOrder(audioOrder).subscribe(() => {
      this.toasterService.showToast('Порядок сохранён', 'success', 'bottom-middle', 3000);
    });
  }

}
