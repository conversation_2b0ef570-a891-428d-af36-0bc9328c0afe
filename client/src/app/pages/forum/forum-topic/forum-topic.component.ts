import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component'
import { FavoritesIconComponent } from "@/components/svg-components/favorites-icon/favorites-icon.component"
import { environment } from "@/env/environment"
import { FileService } from "@/services/file.service"
import { ForumService } from "@/services/forum.service"
import { ProfileService } from "@/services/profile.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, isPlatformBrowser, NgClass, NgIf } from "@angular/common"
import { Component, ElementRef, inject, PLATFORM_ID, ViewChild } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { Meta, Title } from "@angular/platform-browser"
import { ActivatedRoute } from "@angular/router"
import moment from 'moment'
import 'moment/locale/ru'
moment.locale('ru');

@Component({
  selector: 'app-forum-topic',
  standalone: true,
  imports: [
    NgIf,
    ReactiveFormsModule,
    NgClass,
    BreadcrumbComponent,
    CommonModule,
    FavoritesIconComponent,
    FormsModule
],
  templateUrl: './forum-topic.component.html',
  styleUrl: './forum-topic.component.scss',
})
export class ForumTopicComponent {
  protected readonly environment = environment;
  @ViewChild('editorContent') editorContent!: ElementRef;
  @ViewChild('imageInput') imageInput!: ElementRef;
  route = inject(ActivatedRoute);
  forumService = inject(ForumService);
  toasterService = inject(ToasterService);
  fb = inject(FormBuilder)
  fileService = inject(FileService);
  titleService = inject(Title)
  metaService = inject(Meta)
  id = this.route.snapshot.paramMap.get('id')!;
  topic: any
  isCommentSubmiting: boolean = false
  profileService = inject(ProfileService);
  platformId = inject(PLATFORM_ID)
  commentForm = this.fb.group({
    id: null,
    topic: this.id,
    comment: ['', [Validators.required, Validators.maxLength(50000), Validators.minLength(5)]],
    reply: null,
    disableComments: [false]
  })
  isBoldActive: boolean = false;
  isItalicActive: boolean = false;
  isUnderlineActive: boolean = false;
  commentId = this.route.snapshot.queryParamMap.get('commentId') || null

  commentImagePreviews: any = [];

  ngOnInit() {
    this.getTopic()
  }

  get userInitials() {
    return this.topic.user.firstName[0] + this.topic.user.lastName[0];
  }

  get formatDate() {
    return (date: any) => moment(date).format('DD MMM YYYY HH:mm')
  }

  get isRemovableComment() {
    return (access: any) => access.includes('remove');
  }

  get isEditableComment() {
    return (access: any) => access.includes('edit');
  }

  get commentLength() {
    const editorContent = document.querySelector('.editor-content') as HTMLElement;
    return editorContent.innerHTML.length;
  }

  get isModerator() {
    if(!this.profileService?.profile) return false;

    return this.profileService?.profile.groups.some((e: any)=> ['ADMIN', 'FORUM_MODERATOR'].includes(e));
  }

  getTopic() {
    if (isPlatformBrowser(this.platformId)) {
      this.forumService.getTopic(this.id).subscribe((topic: any) => {
        this.topic = topic
        this.commentForm.get('comment')?.reset()
        this.commentForm.get('id')?.reset()
        this.commentForm.get('reply')?.reset()
        this.isCommentSubmiting = false;

        this.titleService.setTitle(topic.name);
        this.metaService.updateTag({ name: 'description', content: topic.content })

        this.commentForm.get('disableComments')?.setValue(topic.disableComments)

        if(this.commentId) {
          setTimeout(() => {
            this.scrollToComment(this.commentId)
          }, 100)
        }
      });
    }
  }

  onInsertImage() {
    this.imageInput.nativeElement.click();
  }

  onImageSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    if (input.files.length > 5 || (this.commentImagePreviews.length + input.files.length) > 5) {
      input.value = '';
      return this.toasterService.showToast('Загрузить возможно не более 5 изображений', 'error', 'bottom-middle', 3000);
    }

    const MAX_FILE_SIZE = 10 * 1024 * 1024;
    for (const file of Array.from(input.files)) {
      if (file.size > MAX_FILE_SIZE) {
        this.toasterService.showToast(`Файл "${file.name}" слишком большой. Максимальный размер: 10 МБ.`, 'error', 'bottom-middle', 3000);
        input.value = '';
        return;
      }
      if (!file.type.startsWith('image/')) {
        this.toasterService.showToast(`Файл "${file.name}" не является изображением.`, 'error', 'bottom-middle', 3000);
        input.value = '';
        return;
      }
    }

    this.fileService.upload(input.files, 'forum-images').subscribe({
      next: (response: any) => {
        const uploadedImages = response.map((file: any) => ({
          id: file.id,
          src: this.environment.serverUrl + '/upload/' + file.name,
          name: file.originalName,
        }));
        this.commentImagePreviews.push(...uploadedImages);
      },
      error: (err: any) => {
        this.toasterService.showToast('Ошибка при загрузке изображений.', 'error', 'bottom-middle', 3000);
        console.error(err);
      },
      complete: () => {
        input.value = '';
      }
    });
  }

  removeCommentImage(index: number) {
    this.commentImagePreviews.splice(index, 1);
  }

  insertImageAtCursor(imgElement: HTMLImageElement) {
    const sel = window.getSelection();
    if (!sel || !sel.rangeCount) return;
    const range = sel.getRangeAt(0);
    range.deleteContents();
    range.insertNode(imgElement);
    range.setStartAfter(imgElement);
    range.setEndAfter(imgElement);
    sel.removeAllRanges();
    sel.addRange(range);
  }

  submitComment() {
    this.isCommentSubmiting = true;

    let finalCommentHtml = this.editorContent.nativeElement.innerHTML;

    if (this.commentImagePreviews.length > 0) {
      const imagesHtml = this.commentImagePreviews.map((preview: any) =>
        `<img src="${preview.src}" style="max-width: 100%; margin: 10px 0;">`
      ).join('');

      finalCommentHtml += `<br>${imagesHtml}`;
    }

    if(finalCommentHtml.length > 50000) {
      return this.toasterService.showToast('Количество символов в сообщении не должно превышать 50000', 'error', 'bottom-middle', 3000);
    }

    this.commentForm.patchValue({ comment: finalCommentHtml });

    this.forumService.sendComment(this.commentForm.value).subscribe({
      next: (response: any) => {
        this.getTopic();
        if (this.editorContent) {
          this.editorContent.nativeElement.innerHTML = '';
        }
        this.commentImagePreviews = [];
        this.isCommentSubmiting = false;
      },
      error: (err: any) => {
        this.toasterService.showToast(err.error.message, 'error', 'bottom-middle', 3000);
        this.commentImagePreviews = [];
        if (this.editorContent) {
          this.editorContent.nativeElement.innerHTML = '';
        }
        this.isCommentSubmiting = false;
      }
    });
  }

  likeComment(id: number) {
    this.forumService.likeComment(id).subscribe((res: any) => this.getTopic())
  }

  favoriteComment(id: number) {
    this.forumService.favoriteComment(id).subscribe((res: any) => this.getTopic())
  }

  likeTopic(id: number) {
    this.forumService.likeTopic(id).subscribe((res: any) => this.getTopic())
  }

  favoriteTopic(id: number) {
    this.forumService.favoriteTopic(id).subscribe((res: any) => this.getTopic())
  }

  removeComment(id: number) {
    this.forumService.removeComment(id).subscribe((res: any) => this.getTopic())
  }


  replyComment(comment: any) {
    this.commentForm.patchValue({ reply: comment })
  }

  cancelReply() {
    this.commentForm.get('reply')?.reset();
    this.commentImagePreviews = [];
  }

  // Метод для обновления значения формы
  updateFormValue(content: string) {
    this.commentForm.get('comment')?.setValue(content);
  }

  editComment(comment: any) {
    // Устанавливаем ID и текст комментария в форму
    this.commentForm.patchValue({
      id: comment.id,
      comment: comment.comment,
      reply: null // Сбрасываем reply, так как мы редактируем, а не отвечаем
    });
    // Устанавливаем HTML в редактор
    setTimeout(() => {
      if (this.editorContent) {
        this.editorContent.nativeElement.innerHTML = comment.comment;
        // Устанавливаем фокус на редактор
        this.editorContent.nativeElement.focus();
        // Создаем диапазон в конце текста
        if (isPlatformBrowser(this.platformId)) {
          const range = document.createRange();
          range.selectNodeContents(this.editorContent.nativeElement);
          range.collapse(false); // Коллапсируем в конец
          const selection = window.getSelection();
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
            // Проверяем форматирование в текущей позиции
            this.checkFormatting();
          }
        }
      }
    });
    // Прокручиваем страницу к форме комментария
    if (isPlatformBrowser(this.platformId)) {
      document.documentElement.scrollTop = document.documentElement.scrollHeight;
    }
  }

  cancelEdit() {
    this.commentForm.reset({
      topic: this.id,
      comment: '',
      reply: null,
      id: null
    });
    if (this.editorContent) {
      this.editorContent.nativeElement.innerHTML = '';
    }
    this.commentImagePreviews = [];
  }

  resetEditor() {
    // Get the editor content element
    const editorContent = document.querySelector('.editor-content') as HTMLElement;
    if (editorContent) {
      // Clear the content
      editorContent.innerHTML = '';
      // Update the form value
      this.updateFormValue('');
    }
  }
  ngAfterViewInit() {
    if (isPlatformBrowser(this.platformId) && this.editorContent) {
      // Добавляем обработчик события selectionchange для документа
      document.addEventListener('selectionchange', () => {
        // Проверяем, находится ли фокус в нашем редакторе
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const container = range.commonAncestorContainer;
          // Проверяем, находится ли выделение внутри нашего редактора
          if (this.editorContent.nativeElement.contains(container)) {
            this.checkFormatting();
          }
        }
      });
    }
  }

  toggleFormat(format: string) {
    console.log('toggleFormat called with format:', format);

    if (isPlatformBrowser(this.platformId) && this.editorContent) {
      const editor = this.editorContent.nativeElement;
      console.log('Editor element:', editor);
      console.log('Editor contentEditable:', editor.contentEditable);
      console.log('Editor innerHTML before:', editor.innerHTML);

      // Simple test: add some text to verify the editor works
      if (format === 'bold') {
        console.log('Testing editor by adding bold text');
        editor.innerHTML += '<strong>BOLD TEST</strong>';
        this.updateFormValue(editor.innerHTML);
        this.checkFormatting();
        return;
      }

      // Ensure the editor has focus
      editor.focus();

      // Save current selection
      const selection = window.getSelection();
      console.log('Selection:', selection);

      if (!selection || selection.rangeCount === 0) {
        console.log('No selection found, creating one at cursor position');
        // If no selection, try to create one at cursor position
        const range = document.createRange();
        range.selectNodeContents(editor);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
      }

      const range = selection.getRangeAt(0);
      console.log('Range:', range);

      // Execute the formatting command
      try {
        console.log('Trying execCommand for format:', format);
        const success = document.execCommand(format, false, undefined);
        console.log('execCommand success:', success);

        if (!success) {
          console.warn('execCommand failed for format:', format);
          // Fallback: manual formatting
          this.manualFormat(format, range);
        }
      } catch (error) {
        console.warn('execCommand failed, using manual approach:', error);
        this.manualFormat(format, range);
      }

      // Restore focus and update state
      editor.focus();

      // Small delay to ensure the command is processed
      setTimeout(() => {
        console.log('Checking formatting after toggle');
        this.checkFormatting();
        this.updateFormValue(editor.innerHTML);
        console.log('Editor innerHTML after formatting:', editor.innerHTML);
      }, 10);
    } else {
      console.log('Platform not browser or editor not found');
    }
  }

  private manualFormat(format: string, range: Range) {
    console.log('manualFormat called with format:', format, 'range:', range);

    if (!range) {
      console.log('No range provided');
      return;
    }

    if (range.collapsed) {
      console.log('Range is collapsed, cannot format empty selection');
      return;
    }

    const selectedText = range.toString();
    console.log('Selected text:', selectedText);

    if (!selectedText) {
      console.log('No selected text');
      return;
    }

    let wrapper: HTMLElement;
    switch (format) {
      case 'bold':
        wrapper = document.createElement('strong');
        break;
      case 'italic':
        wrapper = document.createElement('em');
        break;
      case 'underline':
        wrapper = document.createElement('u');
        break;
      default:
        console.log('Unknown format:', format);
        return;
    }

    console.log('Created wrapper element:', wrapper);

    try {
      // Extract contents and wrap them
      const contents = range.extractContents();
      console.log('Extracted contents:', contents);

      wrapper.appendChild(contents);
      range.insertNode(wrapper);
      console.log('Inserted wrapper into range');

      // Update selection to include the new wrapper
      const newRange = document.createRange();
      newRange.selectNodeContents(wrapper);
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(newRange);
        console.log('Updated selection to new wrapper');
      }
    } catch (error) {
      console.error('Manual formatting failed:', error);
    }
  }
  onSelectionChange() {
    this.checkFormatting();
  }
  // Keep the original formatText for link which doesn't toggle
  formatText(format: string) {
    if (format === 'link') {
      const url = prompt('Введите URL:');
      if (url) {
        document.execCommand('createLink', false, url);
        // Update the form value
        const editorContent = document.querySelector('.editor-content') as HTMLElement;
        if (editorContent) {
          this.updateFormValue(editorContent.innerHTML);
        }
      }
    }
  }

  // Add this method to clear formatting
  clearFormatting() {
    document.execCommand('removeFormat', false);
    // Reset all active states
    this.isBoldActive = false;
    this.isItalicActive = false;
    this.isUnderlineActive = false;
    // Update the form value
    const editorContent = document.querySelector('.editor-content') as HTMLElement;
    if (editorContent) {
      this.updateFormValue(editorContent.innerHTML);
    }
  }
  // Метод для проверки текущего форматирования
  checkFormatting() {
    console.log('checkFormatting called');

    if (isPlatformBrowser(this.platformId)) {
      // Reset states
      this.isBoldActive = false;
      this.isItalicActive = false;
      this.isUnderlineActive = false;

      try {
        // First try queryCommandState
        this.isBoldActive = document.queryCommandState('bold');
        this.isItalicActive = document.queryCommandState('italic');
        this.isUnderlineActive = document.queryCommandState('underline');
        console.log('queryCommandState results - bold:', this.isBoldActive, 'italic:', this.isItalicActive, 'underline:', this.isUnderlineActive);
      } catch (error) {
        console.warn('queryCommandState failed, using fallback method');
      }

      // Fallback: check by examining the selection and parent elements
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        let element = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
          ? range.commonAncestorContainer.parentElement
          : range.commonAncestorContainer as Element;

        console.log('Starting element for formatting check:', element);

        // Walk up the DOM tree to check for formatting
        while (element && element !== this.editorContent?.nativeElement) {
          console.log('Checking element:', element.tagName, element);

          if (this.hasFormatting(element, 'bold')) {
            this.isBoldActive = true;
            console.log('Found bold formatting in element:', element);
          }
          if (this.hasFormatting(element, 'italic')) {
            this.isItalicActive = true;
            console.log('Found italic formatting in element:', element);
          }
          if (this.hasFormatting(element, 'underline')) {
            this.isUnderlineActive = true;
            console.log('Found underline formatting in element:', element);
          }
          element = element.parentElement;
        }
      }

      console.log('Final formatting states - bold:', this.isBoldActive, 'italic:', this.isItalicActive, 'underline:', this.isUnderlineActive);
    }
  }

  // Вспомогательный метод для проверки форматирования элемента
  private hasFormatting(element: Element | null, format: string): boolean {
    if (!element) return false;

    // Check tag names first (more reliable)
    switch (format) {
      case 'bold':
        if (element.tagName === 'B' || element.tagName === 'STRONG') {
          return true;
        }
        break;
      case 'italic':
        if (element.tagName === 'I' || element.tagName === 'EM') {
          return true;
        }
        break;
      case 'underline':
        if (element.tagName === 'U') {
          return true;
        }
        break;
    }

    // Fallback to computed styles
    try {
      const style = window.getComputedStyle(element);
      switch (format) {
        case 'bold':
          const fontWeight = style.fontWeight;
          return fontWeight === 'bold' || fontWeight === '700' || parseInt(fontWeight) >= 700;
        case 'italic':
          return style.fontStyle === 'italic';
        case 'underline':
          return style.textDecoration.includes('underline');
        default:
          return false;
      }
    } catch (error) {
      return false;
    }
  }

  changeDisableComments(e: Event) {
    const checked = (e.target as HTMLInputElement).checked;

    this.forumService.changeDisableComments(this.id, checked).subscribe();
  }

  scrollToComment(commentId: string | null) {
    if (isPlatformBrowser(this.platformId) && commentId) {
      const element = document.getElementById('comment-' + commentId);
      if (element) {
        const elementPosition = element.offsetTop;
        const offsetPosition = elementPosition - 80;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    }
  }
}
