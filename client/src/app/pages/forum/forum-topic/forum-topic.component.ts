import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component'
import { FavoritesIconComponent } from "@/components/svg-components/favorites-icon/favorites-icon.component"
import { environment } from "@/env/environment"
import { FileService } from "@/services/file.service"
import { ForumService } from "@/services/forum.service"
import { ProfileService } from "@/services/profile.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, isPlatformBrowser, NgClass, NgIf } from "@angular/common"
import { Component, ElementRef, inject, PLATFORM_ID, ViewChild } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { Meta, Title } from "@angular/platform-browser"
import { ActivatedRoute } from "@angular/router"
import moment from 'moment'
import 'moment/locale/ru'
moment.locale('ru');

@Component({
  selector: 'app-forum-topic',
  standalone: true,
  imports: [
    NgIf,
    ReactiveFormsModule,
    NgClass,
    BreadcrumbComponent,
    CommonModule,
    FavoritesIconComponent,
    FormsModule
],
  templateUrl: './forum-topic.component.html',
  styleUrl: './forum-topic.component.scss',
})
export class ForumTopicComponent {
  protected readonly environment = environment;
  @ViewChild('editorContent') editorContent!: ElementRef;
  @ViewChild('imageInput') imageInput!: ElementRef;
  route = inject(ActivatedRoute);
  forumService = inject(ForumService);
  toasterService = inject(ToasterService);
  fb = inject(FormBuilder)
  fileService = inject(FileService);
  titleService = inject(Title)
  metaService = inject(Meta)
  id = this.route.snapshot.paramMap.get('id')!;
  topic: any
  isCommentSubmiting: boolean = false
  profileService = inject(ProfileService);
  platformId = inject(PLATFORM_ID)
  commentForm = this.fb.group({
    id: null,
    topic: this.id,
    comment: ['', [Validators.required, Validators.maxLength(50000), Validators.minLength(5)]],
    reply: null,
    disableComments: [false]
  })
  isBoldActive: boolean = false;
  isItalicActive: boolean = false;
  isUnderlineActive: boolean = false;
  commentId = this.route.snapshot.queryParamMap.get('commentId') || null

  commentImagePreviews: any = [];

  ngOnInit() {
    this.getTopic()
  }

  get userInitials() {
    return this.topic.user.firstName[0] + this.topic.user.lastName[0];
  }

  get formatDate() {
    return (date: any) => moment(date).format('DD MMM YYYY HH:mm')
  }

  get isRemovableComment() {
    return (access: any) => access.includes('remove');
  }

  get isEditableComment() {
    return (access: any) => access.includes('edit');
  }

  get commentLength() {
    const editorContent = document.querySelector('.editor-content') as HTMLElement;
    return editorContent.innerHTML.length;
  }

  get isModerator() {
    if(!this.profileService?.profile) return false;

    return this.profileService?.profile.groups.some((e: any)=> ['ADMIN', 'FORUM_MODERATOR'].includes(e));
  }

  getTopic() {
    if (isPlatformBrowser(this.platformId)) {
      this.forumService.getTopic(this.id).subscribe((topic: any) => {
        this.topic = topic
        this.commentForm.get('comment')?.reset()
        this.commentForm.get('id')?.reset()
        this.commentForm.get('reply')?.reset()
        this.isCommentSubmiting = false;

        this.titleService.setTitle(topic.name);
        this.metaService.updateTag({ name: 'description', content: topic.content })

        this.commentForm.get('disableComments')?.setValue(topic.disableComments)

        if(this.commentId) {
          setTimeout(() => {
            this.scrollToComment(this.commentId)
          }, 100)
        }
      });
    }
  }

  onInsertImage() {
    this.imageInput.nativeElement.click();
  }

  onImageSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) return;

    if (input.files.length > 5 || (this.commentImagePreviews.length + input.files.length) > 5) {
      input.value = '';
      return this.toasterService.showToast('Загрузить возможно не более 5 изображений', 'error', 'bottom-middle', 3000);
    }

    const MAX_FILE_SIZE = 10 * 1024 * 1024;
    for (const file of Array.from(input.files)) {
      if (file.size > MAX_FILE_SIZE) {
        this.toasterService.showToast(`Файл "${file.name}" слишком большой. Максимальный размер: 10 МБ.`, 'error', 'bottom-middle', 3000);
        input.value = '';
        return;
      }
      if (!file.type.startsWith('image/')) {
        this.toasterService.showToast(`Файл "${file.name}" не является изображением.`, 'error', 'bottom-middle', 3000);
        input.value = '';
        return;
      }
    }

    this.fileService.upload(input.files, 'forum-images').subscribe({
      next: (response: any) => {
        const uploadedImages = response.map((file: any) => ({
          id: file.id,
          src: this.environment.serverUrl + '/upload/' + file.name,
          name: file.originalName,
        }));
        this.commentImagePreviews.push(...uploadedImages);
      },
      error: (err: any) => {
        this.toasterService.showToast('Ошибка при загрузке изображений.', 'error', 'bottom-middle', 3000);
        console.error(err);
      },
      complete: () => {
        input.value = '';
      }
    });
  }

  removeCommentImage(index: number) {
    this.commentImagePreviews.splice(index, 1);
  }

  insertImageAtCursor(imgElement: HTMLImageElement) {
    const sel = window.getSelection();
    if (!sel || !sel.rangeCount) return;
    const range = sel.getRangeAt(0);
    range.deleteContents();
    range.insertNode(imgElement);
    range.setStartAfter(imgElement);
    range.setEndAfter(imgElement);
    sel.removeAllRanges();
    sel.addRange(range);
  }

  submitComment() {
    this.isCommentSubmiting = true;

    let finalCommentHtml = this.editorContent.nativeElement.innerHTML;

    if (this.commentImagePreviews.length > 0) {
      const imagesHtml = this.commentImagePreviews.map((preview: any) =>
        `<img src="${preview.src}" style="max-width: 100%; margin: 10px 0;">`
      ).join('');

      finalCommentHtml += `<br>${imagesHtml}`;
    }

    if(finalCommentHtml.length > 50000) {
      return this.toasterService.showToast('Количество символов в сообщении не должно превышать 50000', 'error', 'bottom-middle', 3000);
    }

    this.commentForm.patchValue({ comment: finalCommentHtml });

    this.forumService.sendComment(this.commentForm.value).subscribe({
      next: (response: any) => {
        this.getTopic();
        if (this.editorContent) {
          this.editorContent.nativeElement.innerHTML = '';
        }
        this.commentImagePreviews = [];
        this.isCommentSubmiting = false;
      },
      error: (err: any) => {
        this.toasterService.showToast(err.error.message, 'error', 'bottom-middle', 3000);
        this.commentImagePreviews = [];
        if (this.editorContent) {
          this.editorContent.nativeElement.innerHTML = '';
        }
        this.isCommentSubmiting = false;
      }
    });
  }

  likeComment(id: number) {
    this.forumService.likeComment(id).subscribe((res: any) => this.getTopic())
  }

  favoriteComment(id: number) {
    this.forumService.favoriteComment(id).subscribe((res: any) => this.getTopic())
  }

  likeTopic(id: number) {
    this.forumService.likeTopic(id).subscribe((res: any) => this.getTopic())
  }

  favoriteTopic(id: number) {
    this.forumService.favoriteTopic(id).subscribe((res: any) => this.getTopic())
  }

  removeComment(id: number) {
    this.forumService.removeComment(id).subscribe((res: any) => this.getTopic())
  }


  replyComment(comment: any) {
    this.commentForm.patchValue({ reply: comment })
  }

  cancelReply() {
    this.commentForm.get('reply')?.reset();
    this.commentImagePreviews = [];
  }

  // Метод для обновления значения формы
  updateFormValue(content: string) {
    this.commentForm.get('comment')?.setValue(content);
  }

  editComment(comment: any) {
    // Устанавливаем ID и текст комментария в форму
    this.commentForm.patchValue({
      id: comment.id,
      comment: comment.comment,
      reply: null // Сбрасываем reply, так как мы редактируем, а не отвечаем
    });
    // Устанавливаем HTML в редактор
    setTimeout(() => {
      if (this.editorContent) {
        this.editorContent.nativeElement.innerHTML = comment.comment;
        // Устанавливаем фокус на редактор
        this.editorContent.nativeElement.focus();
        // Создаем диапазон в конце текста
        if (isPlatformBrowser(this.platformId)) {
          const range = document.createRange();
          range.selectNodeContents(this.editorContent.nativeElement);
          range.collapse(false); // Коллапсируем в конец
          const selection = window.getSelection();
          if (selection) {
            selection.removeAllRanges();
            selection.addRange(range);
            // Проверяем форматирование в текущей позиции
            this.checkFormatting();
          }
        }
      }
    });
    // Прокручиваем страницу к форме комментария
    if (isPlatformBrowser(this.platformId)) {
      document.documentElement.scrollTop = document.documentElement.scrollHeight;
    }
  }

  cancelEdit() {
    this.commentForm.reset({
      topic: this.id,
      comment: '',
      reply: null,
      id: null
    });
    if (this.editorContent) {
      this.editorContent.nativeElement.innerHTML = '';
    }
    this.commentImagePreviews = [];
  }

  resetEditor() {
    // Get the editor content element
    const editorContent = document.querySelector('.editor-content') as HTMLElement;
    if (editorContent) {
      // Clear the content
      editorContent.innerHTML = '';
      // Update the form value
      this.updateFormValue('');
    }
  }
  ngAfterViewInit() {
    if (isPlatformBrowser(this.platformId) && this.editorContent) {
      // Добавляем обработчик события selectionchange для документа
      document.addEventListener('selectionchange', () => {
        // Проверяем, находится ли фокус в нашем редакторе
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const container = range.commonAncestorContainer;
          // Проверяем, находится ли выделение внутри нашего редактора
          if (this.editorContent.nativeElement.contains(container)) {
            this.checkFormatting();
          }
        }
      });
    }
  }

  toggleFormat(format: string) {
    if (isPlatformBrowser(this.platformId)) {
      // Ensure the editor has focus
      if (this.editorContent) {
        this.editorContent.nativeElement.focus();
      }

      // Execute the formatting command
      try {
        document.execCommand(format, false, undefined);
      } catch (error) {
        console.warn('execCommand failed, trying alternative approach:', error);
      }

      // Small delay to ensure the command is processed
      setTimeout(() => {
        this.checkFormatting();
        // Update the form value
        if (this.editorContent) {
          this.updateFormValue(this.editorContent.nativeElement.innerHTML);
        }
      }, 10);
    }
  }
  onSelectionChange() {
    this.checkFormatting();
  }
  // Keep the original formatText for link which doesn't toggle
  formatText(format: string) {
    if (format === 'link') {
      const url = prompt('Введите URL:');
      if (url) {
        document.execCommand('createLink', false, url);
        // Update the form value
        const editorContent = document.querySelector('.editor-content') as HTMLElement;
        if (editorContent) {
          this.updateFormValue(editorContent.innerHTML);
        }
      }
    }
  }

  // Add this method to clear formatting
  clearFormatting() {
    document.execCommand('removeFormat', false);
    // Reset all active states
    this.isBoldActive = false;
    this.isItalicActive = false;
    this.isUnderlineActive = false;
    // Update the form value
    const editorContent = document.querySelector('.editor-content') as HTMLElement;
    if (editorContent) {
      this.updateFormValue(editorContent.innerHTML);
    }
  }
  // Метод для проверки текущего форматирования
  checkFormatting() {
    if (isPlatformBrowser(this.platformId)) {
      try {
        // Проверяем текущее состояние форматирования в месте каретки
        this.isBoldActive = document.queryCommandState('bold');
        this.isItalicActive = document.queryCommandState('italic');
        this.isUnderlineActive = document.queryCommandState('underline');
      } catch (error) {
        // Fallback: check by examining the selection
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const parentElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
            ? range.commonAncestorContainer.parentElement
            : range.commonAncestorContainer as Element;

          if (parentElement) {
            this.isBoldActive = this.hasFormatting(parentElement, 'bold');
            this.isItalicActive = this.hasFormatting(parentElement, 'italic');
            this.isUnderlineActive = this.hasFormatting(parentElement, 'underline');
          }
        }
      }
    }
  }

  // Вспомогательный метод для проверки форматирования элемента
  private hasFormatting(element: Element | null, format: string): boolean {
    if (!element) return false;

    const style = window.getComputedStyle(element);
    switch (format) {
      case 'bold':
        return style.fontWeight === 'bold' || style.fontWeight === '700' ||
               element.tagName === 'B' || element.tagName === 'STRONG';
      case 'italic':
        return style.fontStyle === 'italic' ||
               element.tagName === 'I' || element.tagName === 'EM';
      case 'underline':
        return style.textDecoration.includes('underline') ||
               element.tagName === 'U';
      default:
        return false;
    }
  }

  changeDisableComments(e: Event) {
    const checked = (e.target as HTMLInputElement).checked;

    this.forumService.changeDisableComments(this.id, checked).subscribe();
  }

  scrollToComment(commentId: string | null) {
    if (isPlatformBrowser(this.platformId) && commentId) {
      const element = document.getElementById('comment-' + commentId);
      if (element) {
        const elementPosition = element.offsetTop;
        const offsetPosition = elementPosition - 80;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    }
  }
}
