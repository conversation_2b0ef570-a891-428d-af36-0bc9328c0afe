<dialog class="stylized_wide" #topicDialog>
  <div (click)="closeDialog()" class="x_bt"></div>
  <form [formGroup]="topicForm">
    <div class="flex flex-col cont_mod">
      <p class="pr_20 text-center">Создать тему</p>
      <div class="format-options"></div>
      <p class="auth_head">
        Название темы
      </p>
      <div class="catg_wrap">
        <input formControlName="name" type="text" placeholder="Название темы" maxlength="500">
        <span class="char-counter">{{topicForm.get('name')?.value?.length || 0}} / 500</span>
      </div>
      <p class="auth_head mt-4">
        Текст
      </p>
      <textarea formControlName="content" cols="30" rows="5" placeholder="Текст темы (минимум 10 символов)"></textarea>
      <div class="custom-file-upload">
        <input type="file" multiple accept="image/*" (change)="uploadFiles($event)" id="fileInput" hidden />
        <label for="fileInput">
          <span class="icon"></span>
          Выбрать файл
        </label>
        <div [ngStyle]="{maxHeight: (modalHeight < 650) ? '175px' : '368px'}" class="preview-wrapper">
          <div class="preview-card" *ngFor="let file of imagePreviews; let i = index">
            <button class="remove-btn" (click)="removeImage(i)"><span></span></button>
            <img *ngIf="file && file.type && file.type.startsWith('image/')" [src]="file.src" alt="Preview" />
            <div class="file-name" *ngIf="!file || !file.type || !file.type.startsWith('image/')">
              {{ file.extension.toUpperCase() }}
            </div>
            <p class="file-name">{{ file.name }}</p>
          </div>
        </div>
      </div>
      <div class="filter-buttons-container flex justify-between mt-4">
        <button class="save-btn" [disabled]="isLoading || topicForm.invalid" (click)="onSubmit()">
          <img class="btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
          <div class="save-btn-label">Опубликовать</div>
        </button>
        <button class="save-btn" (click)="closeDialog()">
          <img class=" btn-backdrop-img" ngSrc="assets/images/Button1_1_ .svg" width="218" height="54" alt="bg">
          <div class="save-btn-label">Закрыть</div>
        </button>
      </div>
    </div>
  </form>
</dialog>
<div *ngIf="category">
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{category.name}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <div class="articles-search relative">
          <input class="theme_i" [(ngModel)]="searchQuery" type="text" placeholder="Поиск по темам">
          <div (click)="showDialog()" class="p_filter">
            <span class="cr_theme">Создать тему</span>
          </div>
          <div class="articles-sort_ custom-dropdown" (click)="toggleDropdownSort()">
            @if (dropdownSortOpen) {
              <div class="dropdown-content">
                @for(option of sortOptions; track option.id) {
                  <div (click)="selectSort(option.value)" class="dropdown-item cat_i" [class.active]="currentSortField === option.value">
                    {{ option.label }}
                    <span class="sort-arrow" *ngIf="currentSortField === option.value">
                      <svg [ngClass]="{'rotate-down': sortDirection === 'Desc', 'rotate-up': sortDirection === 'Asc'}" width="30"
                        height="30" viewBox="0 0 24 24">
                        <path d="M7 10l5 5 5-5H7z" />
                      </svg>
                    </span>
                  </div>
                }
              </div>
            }
          </div>
        </div>
        <div class="ar_wrapp">
          @for(topic of filteredTopics; track topic.id) {
          <div class="article-item m">
            <div class="vis_part relative">
              <div class="art_img" (click)="router.navigate(['/ru/forum/topic/' + topic.id])">
                @if(category.icon) {
                <img style="object-fit: cover" width="66" height="66"
                  [src]="environment.serverUrl + '/upload/' + category.icon.name">
                } @else {
                <img src="assets/images/clouds.webp" alt="image">
                }
              </div>
              <div class="flex justify-between w-full dbl_wr">
                <div class="titl_w" (click)="router.navigate(['/ru/forum/topic/' + topic.id])">
                  <div class="article-title ov_wrap">
                    {{topic.name}}
                  </div>
                  <div class="flex rticle-category">
                    <div class="article-category">Автор: {{topic.user.firstName}} {{topic.user.lastName}}</div>
                    <div class="article-category ml-6">{{getLastComment(topic.comments)}}</div>
                  </div>
                </div>
                <div class="info_bl">
                  <span>
                    <img src="assets/images/icons/cgfh.svg" alt="check">
                    {{topic.views}} просмотра(ов)
                  </span>
                  <span>
                    <img src="assets/images/icons/fframe.svg" alt="chat">
                    {{topic.comments.length}} ответа(ов)
                  </span>
                </div>
                <button type="button" *ngIf="isModerator" (click)="removeTopic($event, topic.id)" class="delete-topic-btn" title="Удалить тему">
                  <img src="assets/images/icons/trash.svg" alt="Удалить" width="16" height="16">
                </button>
              </div>
            </div>
          </div>
          }
        </div>

        <div class="buttn_catg"  *ngIf="page < totalPages && !isLoading">
          <button class="load-more-button" (click)="nextPage()" [disabled]="isLoadingMore">
            <span>Загрузить еще</span>
          </button>
        </div>

      </div>
    </div>
  </div>
</div>
