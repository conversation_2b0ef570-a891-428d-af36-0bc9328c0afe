import { AuthService } from '@/services/auth.service'
import { ContentService } from '@/services/content.service'
import { LazyLoadingService } from '@/services/lazy-loading.service'
import { LibraryService } from "@/services/library.service"
import { ToasterService } from '@/services/toaster.service'
import { CommonModule, isPlatformBrowser, isPlatformServer } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  ElementRef,
  HostListener,
  inject,
  input,
  OnDestroy,
  PLATFORM_ID,
  signal,
  ViewChild
} from '@angular/core'
import { DomSanitizer } from '@angular/platform-browser'
import { RouterLink } from '@angular/router'

@Component({
  selector: 'text-interaction',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './text-interaction.component.html',
  styleUrls: ['./text-interaction.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TextInteractionComponent implements <PERSON><PERSON><PERSON><PERSON>, AfterViewInit {
  // Modern Angular signals-based inputs
  contentId = input<number | null | undefined>(null);
  contentHtml = input<string | undefined>('');
  chapter = input<number>(0);
  type = input<'content' | 'library'>('content');

  // ViewChild for direct DOM access when needed
  @ViewChild('contentContainer', { static: true }) contentContainer!: ElementRef<HTMLDivElement>;

  // Injected services (public for template access)
  contentService = inject(ContentService);
  libraryService = inject(LibraryService);
  toasterService = inject(ToasterService);
  authService = inject(AuthService);

  // Private services
  private sanitizer = inject(DomSanitizer);
  private platformId = inject(PLATFORM_ID);
  private lazyLoadingService = inject(LazyLoadingService);

  // Signals for reactive state management
  selection = signal<string | null>(null);
  quote = signal<string | null>(null);

  // Computed values for performance optimization with memoization
  sanitizedContent = computed(() => {
    const html = this.contentHtml()!;
    const type = this.type();

    // Create cache key based on content and type
    const cacheKey = `${type}-${html.length}-${this.hashString(html)}`;

    // Check cache first
    if (this.contentCache.has(cacheKey)) {
      return this.contentCache.get(cacheKey)!;
    }

    const result = this.sanitizer.bypassSecurityTrustHtml(html);

    // Cache the result
    this.contentCache.set(cacheKey, result);

    // Limit cache size to prevent memory leaks
    if (this.contentCache.size > 10) {
      const firstKey = this.contentCache.keys().next().value;
      if (firstKey) {
        this.contentCache.delete(firstKey);
      }
    }

    return result
  });

  // Cache for processed content to avoid recomputation
  private contentCache = new Map<string, any>();

  // Cleanup references
  private touchSelectionTimeout: any;

  // Utility method for creating cache keys
  private hashString(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString();
  }

  ngAfterViewInit() {
    // Initialize any DOM-dependent functionality here
    if (isPlatformBrowser(this.platformId)) {
      this.initializeLazyLoading();
    }
  }

  ngOnDestroy() {
    // Cleanup timeouts
    if (this.touchSelectionTimeout) {
      clearTimeout(this.touchSelectionTimeout);
    }

    // Cleanup lazy loading
    this.lazyLoadingService.destroy();
  }

  // Platform detection utilities
  isServer(): boolean {
    return isPlatformServer(this.platformId);
  }

  // Content interaction handlers (simplified - only keeping what's actually used)
  handleContentClick(_event: Event) {
    // Placeholder for future content click handling
  }

  handleContentHover(_event: Event) {
    // Placeholder for future content hover handling
  }

  handleContentMouseOut(_event: Event) {
    // Placeholder for future content mouse out handling
  }

  private initializeLazyLoading() {
    // Initialize lazy loading for content
    this.lazyLoadingService.initLazyLoading({
      rootMargin: '100px',
      threshold: 0.1
    });

    // Optimize images in content
    if (this.contentContainer) {
      this.lazyLoadingService.optimizeImages(this.contentContainer.nativeElement);
    }

  }

  @HostListener('mouseup', ['$event'])
  handleMouseUp(_event: MouseEvent) {
    if(!window.getSelection()!.toString()) this.hideContextMenu();
  }

  handleTouchEnd(event: TouchEvent) {
    // Clear any existing timeout
    if (this.touchSelectionTimeout) {
      clearTimeout(this.touchSelectionTimeout);
    }

    // Add a small delay to allow the selection to complete
    this.touchSelectionTimeout = setTimeout(() => {
      const selection = window.getSelection();
      if (selection && selection.toString().trim().length > 0) {
        // Get the last touch position
        const touch = event.changedTouches[event.changedTouches.length - 1];
        // Show the context menu at the touch position
        this.showQuoteContextMenu({
          clientX: touch.clientX,
          clientY: touch.clientY,
          pageX: touch.pageX,
          pageY: touch.pageY
        } as MouseEvent);
      }
    }, 200);
  }

  showQuoteContextMenu(e: MouseEvent) {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const selectedText = selection.toString();
    if (!selectedText || selectedText.length >= 600) return;

    const contentElement = document.getElementById('content');
    if (!contentElement) return;

    const range = selection.getRangeAt(0);

    let isSelectionInContent = false;
    let currentNode: Node | null = range.commonAncestorContainer;

    while (currentNode && currentNode !== document.body) {
      if (currentNode === contentElement) {
        isSelectionInContent = true;
        break;
      }
      currentNode = currentNode.parentNode;
      if (!currentNode) break;
    }

    if (!isSelectionInContent) return;

    this.selection.set(selectedText);
    this.quote.set(selectedText);

    const libraryContext = document.querySelector('.library-context') as HTMLDivElement;
    if (libraryContext) {
      this.positionContextMenu(libraryContext, e);
    }
    return false;
  }

  private getZoomFactor(): number {
    // Check if we're in a zoomed container
    const contentWrapper = document.querySelector('.content-height_wrap:not(.main-contetnt-wrapper)');
    if (contentWrapper) {
      return 0.8; // zoom: 0.8 from styles.scss
    }
    return 1;
  }

  private positionContextMenu(libraryContext: HTMLDivElement, _e: MouseEvent) {
    // Get selection range for more accurate positioning
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const rangeRect = range.getBoundingClientRect();

    // Get zoom factor to adjust coordinates
    const zoomFactor = this.getZoomFactor();

    // Use range position for more accurate placement
    const selectionX = rangeRect.left + (rangeRect.width / 2);

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Get menu dimensions
    const menuWidth = libraryContext.offsetWidth || 325; // Default width from CSS
    const menuHeight = libraryContext.offsetHeight || 150;

    let finalX: number;
    let finalY: number;

    if (this.type() === 'library') {
      // Library-specific positioning adjustments
      const libraryContainer = document.querySelector('.book_text_section') as HTMLElement;
      const libraryRect = libraryContainer?.getBoundingClientRect();

      if (libraryRect) {
        // Position relative to the library container
        finalX = selectionX - (menuWidth / 2);
        finalY = rangeRect.bottom + 10; // Position below selection

        // Adjust for library container offset
        const containerOffsetX = libraryRect.left;
        const containerOffsetY = libraryRect.top;

        // Fine-tune positioning for library layout
        finalX = Math.max(containerOffsetX + 10, finalX);
        finalY = Math.max(containerOffsetY + 10, finalY);
      } else {
        // Fallback positioning for library
        finalX = selectionX - (menuWidth / 2);
        finalY = rangeRect.bottom + 10; // Position below selection
      }
    } else {
      // Content page positioning - position below selection by default
      finalX = selectionX - (menuWidth / 2);
      finalY = rangeRect.bottom + 10; // Position below selection
    }

    // Adjust coordinates for zoom factor
    if (zoomFactor !== 1) {
      finalX = finalX / zoomFactor;
      finalY = finalY / zoomFactor;
    }

    // Ensure menu stays within viewport bounds horizontally
    finalX = Math.max(10, Math.min(finalX, (viewportWidth / zoomFactor) - menuWidth - 10));

    // Vertical positioning with fallback logic
    // If menu would go below viewport, position above selection instead
    if (finalY + menuHeight > (viewportHeight / zoomFactor) - 10) {
      finalY = (rangeRect.top / zoomFactor) - menuHeight - 10;

      // If positioning above would go above viewport, keep it below but adjust
      if (finalY < 10) {
        finalY = Math.max(10, (viewportHeight / zoomFactor) - menuHeight - 10);
      }
    }

    // Final check: if menu is still below viewport after adjustments, position above
    if (finalY + menuHeight > (viewportHeight / zoomFactor) - 10) {
      finalY = (rangeRect.top / zoomFactor) - menuHeight - 10;
    }

    // Apply positioning
    libraryContext.style.position = 'fixed';
    libraryContext.style.left = `${finalX}px`;
    libraryContext.style.top = `${finalY}px`;
    libraryContext.style.zIndex = '1000';

    // Show menu with smooth transition
    libraryContext.style.display = 'block';
    libraryContext.style.opacity = '0';
    libraryContext.style.transform = 'scale(0.95)';

    // Animate in
    requestAnimationFrame(() => {
      libraryContext.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
      libraryContext.style.opacity = '1';
      libraryContext.style.transform = 'scale(1)';
    });
  }

  copySelection(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      const selectedText = this.selection();
      if (selectedText) {
        navigator.clipboard.writeText(selectedText).then(() => {
          this.toasterService.showToast('Текст скопирован в буфер обмена!', 'success', 'bottom-middle', 3000);
          this.hideContextMenu();
        });
      }
    }
  }

  shareQuote(e: Event) {
    if(isPlatformBrowser(this.platformId)) {
      e.stopPropagation();
      this.addQuoteToFavourites(true, true);
    }
  }

  addQuoteToFavourites(copyFlag = false, share = false) {
    const contentId = this.contentId();
    const quote = this.quote();

    if(!contentId || !quote || !this.authService.isAuth) return;
    this.hideContextMenu();

    if(this.type() === 'library') {
      this.libraryService.addQuoteToFavourites(contentId, quote, this.chapter(), share).subscribe((res: any): any => {
        if(typeof res === 'object' && 'error' in res) {
          return this.toasterService.showToast(res.error, 'error', 'bottom-middle', 3000);
        }

        this.quoteAdded(res, copyFlag, share)
      })
    } else {
      this.contentService.addQuoteToFavourites(contentId, quote, share).subscribe((res: any) => {
        

        this.quoteAdded(res, copyFlag, share);
      }
    );
    }
  }

  quoteAdded(id: any, copyFlag = false, share = false) {
    this.quote.set(null);
    const url = location.href + '?quoteId=' + id;
    
    if(!share)
      this.toasterService.showToast('Цитата добавлена!', 'success', 'bottom-middle', 3000);

    if (!this.attemptCopy(url) && copyFlag) {
      this.showShareOptions(url);
    }

  }

private attemptCopy(text: string): boolean {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy'); // Legacy fallback
        document.body.removeChild(textArea);

        if (successful) {
            this.hideContextMenu();
            return true;
        }
    } catch (error) {
        console.error('Copy failed:', error);
    }

    return false;
}

private showShareOptions(url: string) {
    // Проверяем поддержку Web Share API (работает на мобильных)
    if (navigator.share) {
        navigator.share({
            title: 'Цитата',
            url: url
        }).then(() => {

        }).catch(() => {
            this.showUrlForManualCopy(url);
        });
    } else {
        // this.showUrlForManualCopy(url);
    }

    this.hideContextMenu();
}

private showUrlForManualCopy(url: string) {
    // Создаем временный элемент с выделенным URL
    const div = document.createElement('div');
    div.innerHTML = `
        <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                    background: white; padding: 20px; border: 1px solid #ccc; border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 10000; max-width: 90%;">
            <p>Скопируйте ссылку:</p>
            <input type="text" value="${url}" readonly
                   style="width: 100%; padding: 8px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px;"
                   onclick="this.select();">
            <button onclick="this.parentElement.parentElement.remove()"
                    style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                Закрыть
            </button>
        </div>
    `;

    document.body.appendChild(div);

    // Автоматически удаляем через 15 секунд
    setTimeout(() => {
        if (div.parentElement) {
            document.body.removeChild(div);
        }
    }, 15000);
}





  hideContextMenu() {
    const libraryContext = document.querySelector('.library-context') as HTMLDivElement;
    if (libraryContext && libraryContext.style.display !== 'none') {
      // Animate out
      libraryContext.style.opacity = '0';
      libraryContext.style.transform = 'scale(0.95)';

      // Hide after animation
      setTimeout(() => {
        libraryContext.style.display = 'none';
        libraryContext.style.transition = '';
        libraryContext.style.opacity = '';
        libraryContext.style.transform = '';
      }, 200);
    }

    if (window.getSelection) {
      window.getSelection()?.removeAllRanges();
    }
  }

  searchSelection(e: Event) {
    e.preventDefault();
    const selectedText = this.selection();
    if(isPlatformBrowser(this.platformId) && selectedText) {
      const searchUrl = 'https://www.google.com/search?q=' + encodeURIComponent(selectedText);
      window.open(searchUrl, '_blank');
      this.hideContextMenu();
    }
  }

}
